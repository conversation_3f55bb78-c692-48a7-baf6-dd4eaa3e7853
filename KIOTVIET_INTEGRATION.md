# KiotViet Categories Tool Integration

This document describes the implementation of the `kiotviet_get_categories` MCP tool that integrates with the KiotViet API to retrieve product categories.

## Overview

The KiotViet Categories tool provides a comprehensive solution for fetching product categories from the KiotViet API with the following features:

- **OAuth2 Authentication**: Automatic client credentials flow with token caching
- **Flexible Querying**: Support for hierarchical and flat category structures
- **Pagination**: Configurable page sizes and offset-based pagination
- **Filtering**: Filter by modification date and sort by various fields
- **Error Handling**: Comprehensive error handling with proper error codes
- **Caching**: Intelligent token caching to minimize API calls
- **Type Safety**: Full TypeScript support with Zod schema validation

## Architecture

### Components

1. **Token Cache** (`src/utils/cache/tokenCache.ts`)
   - In-memory token storage with expiration handling
   - Automatic token refresh with configurable buffer time
   - Thread-safe operations with proper logging

2. **Authentication Service** (`src/services/kiotviet/authService.ts`)
   - OAuth2 client credentials flow implementation
   - Token retrieval and caching
   - Configuration validation

3. **Categories Service** (`src/services/kiotviet/categoriesService.ts`)
   - KiotViet Categories API integration
   - Query parameter handling
   - Response validation

4. **Tool Logic** (`src/mcp-server/tools/kiotVietCategories/logic.ts`)
   - Input/output schema definitions
   - Main tool processing logic
   - Response transformation

5. **Tool Registration** (`src/mcp-server/tools/kiotVietCategories/registration.ts`)
   - MCP server tool registration
   - Error handling and logging
   - Tool metadata and documentation

## Configuration

Add the following environment variables to your `.env` file:

```bash
# KiotViet API Configuration
KIOTVIET_CLIENT_ID=83a5bcbe-3c39-458c-bdd9-128112cef3f7
KIOTVIET_CLIENT_SECRET=3B52F3A9DDE194966DAE2CE0A478B2DEC15254D6
KIOTVIET_RETAILER=your-store-name
```

**Important**: Replace `your-store-name` with your actual KiotViet store name.

## API Endpoints

### Authentication
- **Endpoint**: `POST https://id.kiotviet.vn/connect/token`
- **Purpose**: Obtain OAuth2 access token
- **Grant Type**: `client_credentials`
- **Scope**: `PublicApi.Access`

### Categories
- **Endpoint**: `GET https://public.kiotapi.com/categories`
- **Purpose**: Retrieve product categories
- **Authentication**: Bearer token in Authorization header
- **Retailer**: Store name in Retailer header

## Tool Usage

### Tool Name
`kiotviet_get_categories`

### Input Parameters

All parameters are optional with sensible defaults:

```typescript
{
  lastModifiedFrom?: string;     // ISO 8601 datetime filter
  pageSize?: number;             // 1-100, default: 20
  currentItem?: number;          // Pagination offset, default: 0
  orderBy?: string;              // Sort field (e.g., "name")
  orderDirection?: "Asc" | "Desc"; // Sort direction, default: "Asc"
  hierachicalData?: boolean;     // Hierarchical structure, default: false
}
```

### Response Structure

```typescript
{
  categories: Array<{
    id: number;
    name: string;
    code?: string;
    parentId: number | null;
    level: number;              // 1-3 hierarchy levels
    isActive: boolean;
    createdDate: string;        // ISO 8601
    modifiedDate: string;       // ISO 8601
    description?: string;
  }>;
  pagination: {
    total: number;              // Total categories available
    pageSize: number;           // Items per page
    currentItem: number;        // Current offset
    hasMore: boolean;           // More items available
  };
  metadata: {
    retailer: string;           // Store name used
    requestedAt: string;        // Request timestamp
    queryParams: object;        // Parameters used
  };
}
```

## Features

### Authentication & Caching
- Automatic OAuth2 token retrieval using client credentials
- Token caching with 5-minute expiration buffer
- Automatic token refresh on expiration
- Secure credential handling

### Query Options
- **Hierarchical Data**: Set `hierachicalData: true` for nested category structure
- **Flat List**: Set `hierachicalData: false` for flat list with modification filtering
- **Pagination**: Use `pageSize` and `currentItem` for pagination
- **Sorting**: Use `orderBy` and `orderDirection` for custom sorting
- **Filtering**: Use `lastModifiedFrom` for date-based filtering (flat mode only)

### Error Handling
- Configuration validation with clear error messages
- HTTP error handling with appropriate status codes
- Network timeout handling (10s auth, 15s categories)
- Comprehensive logging for debugging

### Type Safety
- Full TypeScript implementation
- Zod schema validation for inputs and outputs
- Runtime type checking and validation

## Testing

Run the included test script to validate the implementation:

```bash
node test-kiotviet-tool.js
```

The test validates:
- Configuration loading
- Token cache functionality
- Input schema validation
- Response schema validation
- Tool logic structure

## Usage Examples

### Basic Usage
```json
{
  "pageSize": 20,
  "orderBy": "name",
  "orderDirection": "Asc"
}
```

### Hierarchical Categories
```json
{
  "hierachicalData": true,
  "pageSize": 50
}
```

### Recent Changes
```json
{
  "lastModifiedFrom": "2024-01-01T00:00:00Z",
  "hierachicalData": false,
  "pageSize": 100
}
```

### Pagination
```json
{
  "pageSize": 20,
  "currentItem": 40
}
```

## Limitations

1. **Category Hierarchy**: Maximum 3 levels of hierarchy
2. **Page Size**: Maximum 100 items per page
3. **Token Cache**: In-memory only (not persistent across restarts)
4. **Rate Limiting**: Subject to KiotViet API rate limits

## Security Considerations

1. **Credentials**: Store client credentials securely in environment variables
2. **Token Handling**: Tokens are cached in memory and not logged
3. **HTTPS**: All API communications use HTTPS
4. **Error Messages**: Sensitive information is not exposed in error messages

## Troubleshooting

### Common Issues

1. **Configuration Error**: Ensure all environment variables are set
2. **Authentication Failed**: Verify client credentials are correct
3. **Access Denied**: Check retailer name and API permissions
4. **Service Unavailable**: KiotViet API may be temporarily unavailable

### Debugging

Enable debug logging by setting:
```bash
MCP_LOG_LEVEL=debug
```

Check logs in the `logs/` directory for detailed information.

## Integration Notes

- The tool is automatically registered when the MCP server starts
- No additional setup required beyond environment configuration
- Compatible with both stdio and HTTP MCP transports
- Follows MCP specification for tool registration and execution
