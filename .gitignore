# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# IDE AND EDITOR FILES
# =============================================================================
.idea/
.vscode/
*.swp
*.swo
*~
*.sublime-workspace
*.sublime-project
.history/

# =============================================================================
# NODE.JS & PACKAGE MANAGERS
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm
.pnp.js
.pnp.cjs
.pnp.mjs
.pnp.json
.pnp.ts

# =============================================================================
# TYPESCRIPT & JAVASCRIPT
# =============================================================================
*.tsbuildinfo
.tscache/
*.js.map
*.mjs.map
*.cjs.map
*.d.ts.map
*.d.ts
!*.d.ts.template
*.tgz
.eslintcache
.rollup.cache

# =============================================================================
# PYTHON
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
develop-eggs/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.venv
venv/
ENV/

# =============================================================================
# JAVA
# =============================================================================
*.class
*.jar
*.war
*.nar
*.ear
hs_err_pid*
target/
.gradle/

# =============================================================================
# RUBY
# =============================================================================
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/test/tmp/
/test/version_tmp/
/tmp/
.byebug_history

# =============================================================================
# BUILD & DISTRIBUTION
# =============================================================================
build/
dist/
out/

# =============================================================================
# COMPILED FILES
# =============================================================================
*.com
*.dll
*.exe
*.o

# =============================================================================
# PACKAGE & ARCHIVE FILES
# =============================================================================
*.7z
*.dmg
*.gz
*.iso
*.rar
*.tar
*.tar.gz
*.zip

# =============================================================================
# LOGS & DATABASES
# =============================================================================
*.log
*.sql
*.sqlite
*.sqlite3
logs/

# =============================================================================
# TESTING & COVERAGE
# =============================================================================
coverage/
.nyc_output/

# =============================================================================
# CACHE & TEMPORARY FILES
# =============================================================================
.cache/
.parcel-cache/
*.bak

# =============================================================================
# ENVIRONMENT & CONFIGURATION
# =============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.sample-env
!sample.template.*
mcp-servers.json
mcp-config.json
.wrangler
worker-configuration.d.ts

# =============================================================================
# DEMO & EXAMPLE DIRECTORIES
# =============================================================================

# =============================================================================
# GENERATED DOCUMENTATION
# =============================================================================
docs/api/

# =============================================================================
# APPLICATION SPECIFIC
# =============================================================================
repomix-output*
duckdata/
.claude
data/