/**
 * @fileoverview Simple in-memory token cache utility for storing and managing
 * access tokens with expiration handling. This cache is designed to prevent
 * unnecessary API calls when tokens are still valid.
 * @module src/utils/cache/tokenCache
 */

import { logger, requestContextService } from "../index.js";

/**
 * Represents a cached token with its expiration information.
 */
interface CachedToken {
  /** The access token string */
  token: string;
  /** Timestamp when the token expires (in milliseconds) */
  expiresAt: number;
  /** Optional: When the token was originally issued */
  issuedAt?: number;
  /** Optional: Token type (usually "Bearer") */
  tokenType?: string;
}

/**
 * Simple in-memory token cache for managing access tokens with expiration.
 * This cache automatically handles token expiration and provides methods
 * to store, retrieve, and validate tokens.
 */
export class TokenCache {
  /**
   * Internal storage for cached tokens, keyed by cache key.
   * @private
   */
  private cache: Map<string, CachedToken> = new Map();

  /**
   * Buffer time in milliseconds to refresh tokens before they expire.
   * Default: 5 minutes (300,000 ms)
   * @private
   */
  private readonly expirationBuffer: number;

  /**
   * Creates a new TokenCache instance.
   * @param expirationBuffer - Buffer time in milliseconds to refresh tokens before expiration (default: 5 minutes)
   */
  constructor(expirationBuffer: number = 5 * 60 * 1000) {
    this.expirationBuffer = expirationBuffer;
  }

  /**
   * Stores a token in the cache with expiration information.
   * @param key - Unique identifier for the token (e.g., "kiotviet_access_token")
   * @param token - The access token string
   * @param expiresInSeconds - Token lifetime in seconds
   * @param tokenType - Optional token type (default: "Bearer")
   */
  public set(
    key: string,
    token: string,
    expiresInSeconds: number,
    tokenType: string = "Bearer",
  ): void {
    const context = requestContextService.createRequestContext({
      operation: "TokenCache.set",
      cacheKey: key,
    });

    const now = Date.now();
    const expiresAt = now + expiresInSeconds * 1000;

    const cachedToken: CachedToken = {
      token,
      expiresAt,
      issuedAt: now,
      tokenType,
    };

    this.cache.set(key, cachedToken);

    logger.debug("Token cached successfully", {
      ...context,
      expiresInSeconds,
      expiresAt: new Date(expiresAt).toISOString(),
      tokenType,
    });
  }

  /**
   * Retrieves a valid token from the cache.
   * Returns null if the token doesn't exist or has expired (considering buffer time).
   * @param key - Unique identifier for the token
   * @returns The cached token or null if not found/expired
   */
  public get(key: string): string | null {
    const context = requestContextService.createRequestContext({
      operation: "TokenCache.get",
      cacheKey: key,
    });

    const cachedToken = this.cache.get(key);

    if (!cachedToken) {
      logger.debug("Token not found in cache", context);
      return null;
    }

    const now = Date.now();
    const effectiveExpirationTime = cachedToken.expiresAt - this.expirationBuffer;

    if (now >= effectiveExpirationTime) {
      logger.debug("Token expired or within expiration buffer, removing from cache", {
        ...context,
        expiresAt: new Date(cachedToken.expiresAt).toISOString(),
        effectiveExpirationTime: new Date(effectiveExpirationTime).toISOString(),
        expirationBuffer: this.expirationBuffer,
      });
      
      this.cache.delete(key);
      return null;
    }

    logger.debug("Valid token retrieved from cache", {
      ...context,
      expiresAt: new Date(cachedToken.expiresAt).toISOString(),
      tokenType: cachedToken.tokenType,
    });

    return cachedToken.token;
  }

  /**
   * Checks if a token exists and is valid (not expired considering buffer time).
   * @param key - Unique identifier for the token
   * @returns True if token exists and is valid, false otherwise
   */
  public has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * Removes a token from the cache.
   * @param key - Unique identifier for the token
   * @returns True if token was removed, false if it didn't exist
   */
  public delete(key: string): boolean {
    const context = requestContextService.createRequestContext({
      operation: "TokenCache.delete",
      cacheKey: key,
    });

    const existed = this.cache.has(key);
    const deleted = this.cache.delete(key);

    if (deleted) {
      logger.debug("Token removed from cache", context);
    } else {
      logger.debug("Token not found in cache for deletion", context);
    }

    return existed;
  }

  /**
   * Clears all tokens from the cache.
   */
  public clear(): void {
    const context = requestContextService.createRequestContext({
      operation: "TokenCache.clear",
    });

    const count = this.cache.size;
    this.cache.clear();

    logger.info("Token cache cleared", {
      ...context,
      clearedTokens: count,
    });
  }

  /**
   * Gets the number of tokens currently in the cache.
   * @returns Number of cached tokens
   */
  public size(): number {
    return this.cache.size;
  }

  /**
   * Gets information about a cached token without returning the token itself.
   * Useful for debugging and monitoring.
   * @param key - Unique identifier for the token
   * @returns Token information or null if not found
   */
  public getTokenInfo(key: string): {
    expiresAt: Date;
    issuedAt?: Date;
    tokenType?: string;
    isExpired: boolean;
    timeUntilExpiration: number;
  } | null {
    const cachedToken = this.cache.get(key);

    if (!cachedToken) {
      return null;
    }

    const now = Date.now();
    const effectiveExpirationTime = cachedToken.expiresAt - this.expirationBuffer;

    return {
      expiresAt: new Date(cachedToken.expiresAt),
      issuedAt: cachedToken.issuedAt ? new Date(cachedToken.issuedAt) : undefined,
      tokenType: cachedToken.tokenType,
      isExpired: now >= effectiveExpirationTime,
      timeUntilExpiration: Math.max(0, effectiveExpirationTime - now),
    };
  }
}

/**
 * Default singleton instance of TokenCache for application-wide use.
 * Uses a 5-minute expiration buffer by default.
 */
export const tokenCache = new TokenCache();
