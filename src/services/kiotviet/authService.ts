/**
 * @fileoverview KiotViet API authentication service implementing OAuth2 client credentials flow.
 * This service handles access token retrieval, caching, and automatic refresh for KiotViet API integration.
 * @module src/services/kiotviet/authService
 */

import { z } from "zod";
import { config } from "../../config/index.js";
import { BaseErrorCode, McpError } from "../../types-global/errors.js";
import {
  ErrorHandler,
  fetchWithTimeout,
  logger,
  type RequestContext,
} from "../../utils/index.js";
import { tokenCache } from "../../utils/cache/tokenCache.js";

/**
 * KiotViet OAuth2 token endpoint URL
 */
const KIOTVIET_TOKEN_ENDPOINT = "https://id.kiotviet.vn/connect/token";

/**
 * Cache key for KiotViet access token
 */
const KIOTVIET_TOKEN_CACHE_KEY = "kiotviet_access_token";

/**
 * Request timeout for authentication API calls (in milliseconds)
 */
const AUTH_REQUEST_TIMEOUT_MS = 10000; // 10 seconds

/**
 * Zod schema for validating KiotViet OAuth2 token response
 */
const KiotVietTokenResponseSchema = z.object({
  access_token: z.string().describe("The access token for API authentication"),
  token_type: z.string().describe("Token type, typically 'Bearer'"),
  expires_in: z.number().int().positive().describe("Token lifetime in seconds"),
  scope: z.string().optional().describe("Granted scopes for the token"),
});

/**
 * TypeScript type for KiotViet token response
 */
type KiotVietTokenResponse = z.infer<typeof KiotVietTokenResponseSchema>;

/**
 * Validates that KiotViet configuration is available
 * @throws {McpError} If KiotViet configuration is missing
 */
function validateKiotVietConfig(): void {
  if (!config.kiotviet) {
    throw new McpError(
      BaseErrorCode.CONFIGURATION_ERROR,
      "KiotViet configuration is missing. Please set KIOTVIET_CLIENT_ID, KIOTVIET_CLIENT_SECRET, and KIOTVIET_RETAILER environment variables.",
    );
  }

  const { clientId, clientSecret, retailer } = config.kiotviet;
  
  if (!clientId || !clientSecret || !retailer) {
    throw new McpError(
      BaseErrorCode.CONFIGURATION_ERROR,
      "Incomplete KiotViet configuration. All of KIOTVIET_CLIENT_ID, KIOTVIET_CLIENT_SECRET, and KIOTVIET_RETAILER are required.",
    );
  }
}

/**
 * Retrieves a new access token from KiotViet OAuth2 endpoint
 * @param context - Request context for logging and tracing
 * @returns Promise resolving to the token response
 * @throws {McpError} If authentication fails
 */
async function fetchNewAccessToken(
  context: RequestContext,
): Promise<KiotVietTokenResponse> {
  validateKiotVietConfig();
  
  const { clientId, clientSecret } = config.kiotviet!;

  logger.info("Requesting new access token from KiotViet", {
    ...context,
    endpoint: KIOTVIET_TOKEN_ENDPOINT,
    clientId,
  });

  // Prepare request body as URL-encoded form data
  const requestBody = new URLSearchParams({
    scopes: "PublicApi.Access",
    grant_type: "client_credentials",
    client_id: clientId,
    client_secret: clientSecret,
  });

  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      "User-Agent": `${config.mcpServerName}/${config.mcpServerVersion}`,
    },
    body: requestBody.toString(),
  };

  return ErrorHandler.tryCatch(
    async () => {
      const response = await fetchWithTimeout(
        KIOTVIET_TOKEN_ENDPOINT,
        AUTH_REQUEST_TIMEOUT_MS,
        context,
        requestOptions,
      );

      if (!response.ok) {
        const errorText = await response.text();
        logger.error("KiotViet authentication failed", {
          ...context,
          httpStatus: response.status,
          httpStatusText: response.statusText,
          responseBody: errorText,
        });

        throw new McpError(
          response.status === 401 || response.status === 403
            ? BaseErrorCode.UNAUTHORIZED
            : BaseErrorCode.SERVICE_UNAVAILABLE,
          `KiotViet authentication failed: ${response.status} ${response.statusText}`,
          {
            httpStatus: response.status,
            httpStatusText: response.statusText,
            responseBody: errorText,
          },
        );
      }

      const responseData = await response.json();
      
      // Validate response structure
      const validatedResponse = KiotVietTokenResponseSchema.parse(responseData);

      logger.info("Successfully obtained KiotViet access token", {
        ...context,
        tokenType: validatedResponse.token_type,
        expiresIn: validatedResponse.expires_in,
        scope: validatedResponse.scope,
      });

      return validatedResponse;
    },
    {
      operation: "fetchKiotVietAccessToken",
      context,
      errorCode: BaseErrorCode.SERVICE_UNAVAILABLE,
    },
  );
}

/**
 * Gets a valid KiotViet access token, using cache when possible or fetching a new one
 * @param context - Request context for logging and tracing
 * @returns Promise resolving to a valid access token
 * @throws {McpError} If authentication fails or configuration is invalid
 */
export async function getKiotVietAccessToken(
  context: RequestContext,
): Promise<string> {
  logger.debug("Retrieving KiotViet access token", context);

  // Try to get cached token first
  const cachedToken = tokenCache.get(KIOTVIET_TOKEN_CACHE_KEY);
  
  if (cachedToken) {
    logger.debug("Using cached KiotViet access token", context);
    return cachedToken;
  }

  logger.debug("No valid cached token found, fetching new token", context);

  // Fetch new token
  const tokenResponse = await fetchNewAccessToken(context);

  // Cache the new token
  tokenCache.set(
    KIOTVIET_TOKEN_CACHE_KEY,
    tokenResponse.access_token,
    tokenResponse.expires_in,
    tokenResponse.token_type,
  );

  logger.info("KiotViet access token cached successfully", {
    ...context,
    expiresIn: tokenResponse.expires_in,
  });

  return tokenResponse.access_token;
}

/**
 * Clears the cached KiotViet access token, forcing a fresh token on next request
 * @param context - Request context for logging and tracing
 */
export function clearKiotVietAccessToken(context: RequestContext): void {
  logger.info("Clearing cached KiotViet access token", context);
  tokenCache.delete(KIOTVIET_TOKEN_CACHE_KEY);
}

/**
 * Gets information about the current cached KiotViet token (for debugging/monitoring)
 * @returns Token information or null if no token is cached
 */
export function getKiotVietTokenInfo(): {
  expiresAt: Date;
  issuedAt?: Date;
  tokenType?: string;
  isExpired: boolean;
  timeUntilExpiration: number;
} | null {
  return tokenCache.getTokenInfo(KIOTVIET_TOKEN_CACHE_KEY);
}
