/**
 * @fileoverview KiotViet Categories API service for retrieving product categories.
 * This service handles API requests to fetch categories with proper authentication,
 * query parameters, and error handling.
 * @module src/services/kiotviet/categoriesService
 */

import { z } from "zod";
import { config } from "../../config/index.js";
import { BaseErrorCode, McpError } from "../../types-global/errors.js";
import {
  ErrorHandler,
  fetchWithTimeout,
  logger,
  type RequestContext,
} from "../../utils/index.js";
import { getKiotVietAccessToken } from "./authService.js";

/**
 * KiotViet Categories API endpoint URL
 */
const KIOTVIET_CATEGORIES_ENDPOINT = "https://public.kiotapi.com/categories";

/**
 * Request timeout for categories API calls (in milliseconds)
 */
const CATEGORIES_REQUEST_TIMEOUT_MS = 15000; // 15 seconds

/**
 * Zod schema for category query parameters
 */
export const CategoriesQueryParamsSchema = z.object({
  lastModifiedFrom: z
    .string()
    .datetime()
    .optional()
    .describe("Filter by last modification date (ISO 8601 format)"),
  pageSize: z
    .number()
    .int()
    .min(1)
    .max(100)
    .default(20)
    .describe("Number of items per page (1-100, default: 20)"),
  currentItem: z
    .number()
    .int()
    .min(0)
    .default(0)
    .describe("Pagination offset (default: 0)"),
  orderBy: z
    .string()
    .optional()
    .describe("Sort field (e.g., 'name')"),
  orderDirection: z
    .enum(["Asc", "Desc"])
    .default("Asc")
    .describe("Sort direction: 'Asc' for ascending, 'Desc' for descending"),
  hierachicalData: z
    .boolean()
    .default(false)
    .describe("true = hierarchical structure (ignores lastModifiedFrom), false = flat list"),
});

/**
 * TypeScript type for category query parameters
 */
export type CategoriesQueryParams = z.infer<typeof CategoriesQueryParamsSchema>;

/**
 * Zod schema for a single category item
 */
const CategoryItemSchema = z.object({
  id: z.number().int().describe("Category ID"),
  name: z.string().describe("Category name"),
  code: z.string().optional().describe("Category code"),
  parentId: z.number().int().nullable().describe("Parent category ID (null for root categories)"),
  level: z.number().int().min(1).max(3).describe("Hierarchical level (1-3)"),
  isActive: z.boolean().describe("Whether the category is active"),
  createdDate: z.string().datetime().describe("Category creation date"),
  modifiedDate: z.string().datetime().describe("Category last modification date"),
  description: z.string().optional().describe("Category description"),
});

/**
 * TypeScript type for a category item
 */
export type CategoryItem = z.infer<typeof CategoryItemSchema>;

/**
 * Zod schema for the categories API response
 */
const CategoriesResponseSchema = z.object({
  data: z.array(CategoryItemSchema).describe("Array of category items"),
  total: z.number().int().min(0).describe("Total number of categories"),
  pageSize: z.number().int().min(1).describe("Number of items per page"),
  currentItem: z.number().int().min(0).describe("Current pagination offset"),
});

/**
 * TypeScript type for categories API response
 */
export type CategoriesResponse = z.infer<typeof CategoriesResponseSchema>;

/**
 * Validates that KiotViet configuration is available
 * @throws {McpError} If KiotViet configuration is missing
 */
function validateKiotVietConfig(): void {
  if (!config.kiotviet) {
    throw new McpError(
      BaseErrorCode.CONFIGURATION_ERROR,
      "KiotViet configuration is missing. Please set KIOTVIET_CLIENT_ID, KIOTVIET_CLIENT_SECRET, and KIOTVIET_RETAILER environment variables.",
    );
  }

  const { retailer } = config.kiotviet;
  
  if (!retailer) {
    throw new McpError(
      BaseErrorCode.CONFIGURATION_ERROR,
      "KiotViet retailer name is required. Please set KIOTVIET_RETAILER environment variable.",
    );
  }
}

/**
 * Builds query string from validated parameters
 * @param params - Validated query parameters
 * @returns URL search params string
 */
function buildQueryString(params: CategoriesQueryParams): string {
  const searchParams = new URLSearchParams();

  if (params.lastModifiedFrom) {
    searchParams.append("lastModifiedFrom", params.lastModifiedFrom);
  }
  
  searchParams.append("pageSize", params.pageSize.toString());
  searchParams.append("currentItem", params.currentItem.toString());
  
  if (params.orderBy) {
    searchParams.append("orderBy", params.orderBy);
  }
  
  searchParams.append("orderDirection", params.orderDirection);
  searchParams.append("hierachicalData", params.hierachicalData.toString());

  return searchParams.toString();
}

/**
 * Fetches product categories from KiotViet API
 * @param queryParams - Query parameters for filtering and pagination
 * @param context - Request context for logging and tracing
 * @returns Promise resolving to categories response
 * @throws {McpError} If API request fails or configuration is invalid
 */
export async function fetchKiotVietCategories(
  queryParams: CategoriesQueryParams,
  context: RequestContext,
): Promise<CategoriesResponse> {
  validateKiotVietConfig();
  
  const { retailer } = config.kiotviet!;

  logger.info("Fetching categories from KiotViet API", {
    ...context,
    retailer,
    queryParams,
  });

  // Get access token
  const accessToken = await getKiotVietAccessToken(context);

  // Build request URL with query parameters
  const queryString = buildQueryString(queryParams);
  const requestUrl = queryString 
    ? `${KIOTVIET_CATEGORIES_ENDPOINT}?${queryString}`
    : KIOTVIET_CATEGORIES_ENDPOINT;

  const requestOptions = {
    method: "GET",
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Retailer": retailer,
      "User-Agent": `${config.mcpServerName}/${config.mcpServerVersion}`,
      "Accept": "application/json",
    },
  };

  return ErrorHandler.tryCatch(
    async () => {
      logger.debug("Making request to KiotViet Categories API", {
        ...context,
        url: requestUrl,
        headers: {
          ...requestOptions.headers,
          Authorization: "[REDACTED]", // Don't log the actual token
        },
      });

      const response = await fetchWithTimeout(
        requestUrl,
        CATEGORIES_REQUEST_TIMEOUT_MS,
        context,
        requestOptions,
      );

      if (!response.ok) {
        const errorText = await response.text();
        logger.error("KiotViet Categories API request failed", {
          ...context,
          httpStatus: response.status,
          httpStatusText: response.statusText,
          responseBody: errorText,
          requestUrl,
        });

        // Handle specific error cases
        if (response.status === 401) {
          throw new McpError(
            BaseErrorCode.UNAUTHORIZED,
            "KiotViet API authentication failed. The access token may be invalid or expired.",
            {
              httpStatus: response.status,
              responseBody: errorText,
            },
          );
        }

        if (response.status === 403) {
          throw new McpError(
            BaseErrorCode.FORBIDDEN,
            "Access denied to KiotViet Categories API. Check retailer name and API permissions.",
            {
              httpStatus: response.status,
              responseBody: errorText,
            },
          );
        }

        throw new McpError(
          BaseErrorCode.SERVICE_UNAVAILABLE,
          `KiotViet Categories API request failed: ${response.status} ${response.statusText}`,
          {
            httpStatus: response.status,
            httpStatusText: response.statusText,
            responseBody: errorText,
          },
        );
      }

      const responseData = await response.json();
      
      // Validate response structure
      const validatedResponse = CategoriesResponseSchema.parse(responseData);

      logger.info("Successfully fetched categories from KiotViet API", {
        ...context,
        totalCategories: validatedResponse.total,
        returnedCategories: validatedResponse.data.length,
        pageSize: validatedResponse.pageSize,
        currentItem: validatedResponse.currentItem,
      });

      return validatedResponse;
    },
    {
      operation: "fetchKiotVietCategories",
      context,
      input: queryParams,
      errorCode: BaseErrorCode.SERVICE_UNAVAILABLE,
    },
  );
}
