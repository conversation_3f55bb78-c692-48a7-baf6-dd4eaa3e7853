{"mcpServers": {"atlas-mcp-server": {"command": "node", "args": ["/path/to/atlas-mcp-server/dist/index.js"], "env": {"NEO4J_URI": "bolt://localhost:7687", "NEO4J_USER": "neo4j", "NEO4J_PASSWORD": "password123", "LOG_LEVEL": "debug", "NODE_ENV": "development"}, "transportType": "stdio", "disabled": false, "autoApprove": false}, "github-mcp-server": {"command": "node", "args": ["/path/to/github-mcp-server/build/index.js"], "env": {"GITHUB_TOKEN": "your-github-token"}, "transportType": "stdio", "disabled": false, "autoApprove": false}, "example-http-server": {"command": "http://localhost:3010", "args": [], "transportType": "http", "disabled": false, "autoApprove": true}}}