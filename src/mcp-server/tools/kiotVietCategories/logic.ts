/**
 * @fileoverview Defines the core logic, schemas, and types for the `kiotviet_get_categories` tool.
 * This tool fetches product categories from the KiotViet API with proper authentication,
 * caching, and error handling.
 * @module src/mcp-server/tools/kiotVietCategories/logic
 */

import { z } from "zod";
import { BaseErrorCode, McpError } from "../../../types-global/errors.js";
import {
  logger,
  type RequestContext,
} from "../../../utils/index.js";
import {
  fetchKiotVietCategories,
  CategoriesQueryParamsSchema,
  type CategoryItem,
  type CategoriesResponse,
} from "../../../services/kiotviet/categoriesService.js";

/**
 * Zod schema for validating input arguments for the `kiotviet_get_categories` tool.
 */
export const KiotVietCategoriesInputSchema = z
  .object({
    lastModifiedFrom: z
      .string()
      .datetime()
      .optional()
      .describe(
        "Optional: Filter categories by last modification date (ISO 8601 format, e.g., '2024-01-01T00:00:00Z'). When hierachicalData is true, this parameter is ignored.",
      ),
    pageSize: z
      .number()
      .int()
      .min(1)
      .max(100)
      .default(20)
      .describe(
        "Optional: Number of categories to return per page (1-100, default: 20).",
      ),
    currentItem: z
      .number()
      .int()
      .min(0)
      .default(0)
      .describe(
        "Optional: Pagination offset, starting from 0 (default: 0).",
      ),
    orderBy: z
      .string()
      .optional()
      .describe(
        "Optional: Field to sort by (e.g., 'name', 'createdDate', 'modifiedDate').",
      ),
    orderDirection: z
      .enum(["Asc", "Desc"])
      .default("Asc")
      .describe(
        "Optional: Sort direction - 'Asc' for ascending (default), 'Desc' for descending.",
      ),
    hierachicalData: z
      .boolean()
      .default(false)
      .describe(
        "Optional: If true, returns categories in hierarchical structure (ignores lastModifiedFrom). If false, returns flat list respecting lastModifiedFrom filter (default: false).",
      ),
  })
  .describe(
    "Input parameters for retrieving product categories from KiotViet API. All parameters are optional with sensible defaults.",
  );

/**
 * TypeScript type inferred from `KiotVietCategoriesInputSchema`.
 */
export type KiotVietCategoriesInput = z.infer<typeof KiotVietCategoriesInputSchema>;

/**
 * Zod schema for the successful response of the `kiotviet_get_categories` tool.
 */
export const KiotVietCategoriesResponseSchema = z.object({
  categories: z
    .array(
      z.object({
        id: z.number().int().describe("Category ID"),
        name: z.string().describe("Category name"),
        code: z.string().optional().describe("Category code"),
        parentId: z.number().int().nullable().describe("Parent category ID (null for root categories)"),
        level: z.number().int().min(1).max(3).describe("Hierarchical level (1-3)"),
        isActive: z.boolean().describe("Whether the category is active"),
        createdDate: z.string().datetime().describe("Category creation date"),
        modifiedDate: z.string().datetime().describe("Category last modification date"),
        description: z.string().optional().describe("Category description"),
      }),
    )
    .describe("Array of product categories"),
  pagination: z
    .object({
      total: z.number().int().min(0).describe("Total number of categories available"),
      pageSize: z.number().int().min(1).describe("Number of items per page"),
      currentItem: z.number().int().min(0).describe("Current pagination offset"),
      hasMore: z.boolean().describe("Whether there are more categories available"),
    })
    .describe("Pagination information"),
  metadata: z
    .object({
      retailer: z.string().describe("KiotViet retailer name used for the request"),
      requestedAt: z.string().datetime().describe("ISO 8601 timestamp when the request was made"),
      queryParams: z
        .object({
          lastModifiedFrom: z.string().datetime().optional(),
          pageSize: z.number().int(),
          currentItem: z.number().int(),
          orderBy: z.string().optional(),
          orderDirection: z.enum(["Asc", "Desc"]),
          hierachicalData: z.boolean(),
        })
        .describe("Query parameters used for the request"),
    })
    .describe("Request metadata and configuration"),
});

/**
 * TypeScript type for the tool response structure.
 */
export type KiotVietCategoriesResponse = z.infer<typeof KiotVietCategoriesResponseSchema>;

/**
 * Transforms API response categories to tool response format
 * @param categories - Categories from API response
 * @returns Transformed categories array
 */
function transformCategories(categories: CategoryItem[]): KiotVietCategoriesResponse["categories"] {
  return categories.map((category) => ({
    id: category.id,
    name: category.name,
    code: category.code,
    parentId: category.parentId,
    level: category.level,
    isActive: category.isActive,
    createdDate: category.createdDate,
    modifiedDate: category.modifiedDate,
    description: category.description,
  }));
}

/**
 * Processes the core logic for the `kiotviet_get_categories` tool.
 * It validates input, calls the KiotViet Categories API, and returns formatted results.
 * @param params - The validated input parameters for the tool.
 * @param context - The request context for logging and tracing.
 * @returns A promise that resolves to an object containing the categories data.
 * @throws {McpError} If the API request fails or configuration is invalid.
 */
export async function kiotVietCategoriesLogic(
  params: KiotVietCategoriesInput,
  context: RequestContext,
): Promise<KiotVietCategoriesResponse> {
  logger.debug("Processing kiotviet_get_categories logic", {
    ...context,
    toolInput: params,
  });

  // Validate and transform input parameters for the API service
  const queryParams = CategoriesQueryParamsSchema.parse(params);

  logger.info("Fetching categories from KiotViet API", {
    ...context,
    queryParams,
  });

  try {
    // Call the categories service
    const apiResponse: CategoriesResponse = await fetchKiotVietCategories(
      queryParams,
      context,
    );

    // Transform the response
    const transformedCategories = transformCategories(apiResponse.data);

    // Calculate if there are more items available
    const hasMore = apiResponse.currentItem + apiResponse.data.length < apiResponse.total;

    const toolResponse: KiotVietCategoriesResponse = {
      categories: transformedCategories,
      pagination: {
        total: apiResponse.total,
        pageSize: apiResponse.pageSize,
        currentItem: apiResponse.currentItem,
        hasMore,
      },
      metadata: {
        retailer: process.env.KIOTVIET_RETAILER || "unknown",
        requestedAt: new Date().toISOString(),
        queryParams: {
          lastModifiedFrom: params.lastModifiedFrom,
          pageSize: params.pageSize,
          currentItem: params.currentItem,
          orderBy: params.orderBy,
          orderDirection: params.orderDirection,
          hierachicalData: params.hierachicalData,
        },
      },
    };

    logger.notice("KiotViet categories fetched and processed successfully", {
      ...context,
      categoriesCount: transformedCategories.length,
      totalAvailable: apiResponse.total,
      hasMore,
    });

    return toolResponse;
  } catch (error) {
    // Re-throw McpError instances as-is
    if (error instanceof McpError) {
      throw error;
    }

    // Wrap other errors
    logger.error("Unexpected error in kiotVietCategoriesLogic", {
      ...context,
      error: error instanceof Error ? error.message : String(error),
      errorName: error instanceof Error ? error.name : "Unknown",
    });

    throw new McpError(
      BaseErrorCode.INTERNAL_ERROR,
      "An unexpected error occurred while fetching KiotViet categories.",
      {
        originalError: error instanceof Error ? error.message : String(error),
        originalErrorName: error instanceof Error ? error.name : "Unknown",
      },
    );
  }
}
