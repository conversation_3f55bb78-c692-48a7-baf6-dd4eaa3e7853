/**
 * @fileoverview Handles the registration of the `kiotviet_get_categories` tool
 * with an MCP server instance. This tool fetches product categories from the
 * KiotViet API with authentication, caching, and comprehensive error handling.
 * @module src/mcp-server/tools/kiotVietCategories/registration
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { BaseErrorCode, McpError } from "../../../types-global/errors.js";
import {
  ErrorHandler,
  logger,
  RequestContext,
  requestContextService,
} from "../../../utils/index.js";
import {
  KiotVietCategoriesInput,
  KiotVietCategoriesInputSchema,
  kiotVietCategoriesLogic,
  KiotVietCategoriesResponseSchema,
} from "./logic.js";

/**
 * Registers the 'kiotviet_get_categories' tool and its handler with the MCP server.
 *
 * @param server - The MCP server instance to register the tool with.
 * @returns A promise that resolves when tool registration is complete.
 */
export const registerKiotVietCategoriesTool = async (
  server: McpServer,
): Promise<void> => {
  const toolName = "kiotviet_get_categories";
  const toolDescription = `Retrieves product categories from the KiotViet API with comprehensive filtering and pagination options.

Features:
- OAuth2 authentication with automatic token caching and refresh
- Hierarchical or flat category structure options
- Flexible filtering by modification date
- Pagination support with configurable page sizes
- Sorting by various fields (name, dates, etc.)
- Comprehensive error handling and logging

The tool supports up to 3 levels of category hierarchy. Parent categories cannot be deleted if they contain child categories, and child categories cannot be deleted if they are currently in use.

Authentication is handled automatically using configured KiotViet client credentials (KIOTVIET_CLIENT_ID, KIOTVIET_CLIENT_SECRET) and the retailer name (KIOTVIET_RETAILER).`;

  const registrationContext: RequestContext =
    requestContextService.createRequestContext({
      operation: "RegisterTool",
      toolName: toolName,
    });

  logger.info(`Registering tool: '${toolName}'`, registrationContext);

  await ErrorHandler.tryCatch(
    async () => {
      server.registerTool(
        toolName,
        {
          title: "Get KiotViet Product Categories",
          description: toolDescription,
          inputSchema: KiotVietCategoriesInputSchema.shape,
          outputSchema: KiotVietCategoriesResponseSchema.shape,
          annotations: {
            readOnlyHint: true,
            openWorldHint: true,
          },
        },
        async (params: KiotVietCategoriesInput) => {
          const handlerContext: RequestContext =
            requestContextService.createRequestContext({
              parentRequestId: registrationContext.requestId,
              operation: "HandleToolRequest",
              toolName: toolName,
              input: params,
            });

          try {
            const result = await kiotVietCategoriesLogic(params, handlerContext);
            
            // Return both structuredContent for modern clients and
            // stringified content for backward compatibility.
            return {
              structuredContent: result,
              content: [
                { type: "text", text: JSON.stringify(result, null, 2) },
              ],
            };
          } catch (error) {
            const handledError = ErrorHandler.handleError(error, {
              operation: "kiotVietCategoriesToolHandler",
              context: handlerContext,
              input: params,
            });

            const mcpError =
              handledError instanceof McpError
                ? handledError
                : new McpError(
                    BaseErrorCode.INTERNAL_ERROR,
                    "An unexpected error occurred while fetching KiotViet categories.",
                    { originalErrorName: handledError.name },
                  );

            // Log the error with appropriate level based on error type
            if (mcpError.code === BaseErrorCode.CONFIGURATION_ERROR) {
              logger.error("KiotViet configuration error", {
                ...handlerContext,
                errorCode: mcpError.code,
                errorMessage: mcpError.message,
              });
            } else if (mcpError.code === BaseErrorCode.UNAUTHORIZED || 
                       mcpError.code === BaseErrorCode.FORBIDDEN) {
              logger.warning("KiotViet authentication/authorization error", {
                ...handlerContext,
                errorCode: mcpError.code,
                errorMessage: mcpError.message,
              });
            } else if (mcpError.code === BaseErrorCode.SERVICE_UNAVAILABLE) {
              logger.warning("KiotViet API service unavailable", {
                ...handlerContext,
                errorCode: mcpError.code,
                errorMessage: mcpError.message,
              });
            } else {
              logger.error("Unexpected error in KiotViet categories tool", {
                ...handlerContext,
                errorCode: mcpError.code,
                errorMessage: mcpError.message,
              });
            }

            return {
              isError: true,
              content: [{ type: "text", text: `Error: ${mcpError.message}` }],
            };
          }
        },
      );

      logger.info(
        `Tool '${toolName}' registered successfully.`,
        registrationContext,
      );
    },
    {
      operation: `RegisteringTool_${toolName}`,
      context: registrationContext,
      errorCode: BaseErrorCode.INITIALIZATION_FAILED,
      critical: true,
    },
  );
};
