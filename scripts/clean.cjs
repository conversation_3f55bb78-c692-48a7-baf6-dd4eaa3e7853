#!/usr/bin/env node

/**
 * @fileoverview Utility script to clean build artifacts and temporary directories.
 * @module scripts/clean
 *   By default, it removes the 'dist' and 'logs' directories.
 *   Custom directories can be specified as command-line arguments.
 *   Works on all platforms using Node.js path normalization.
 *   Compatible with Node.js 14.21.3+ using CommonJS
 *
 * @example
 * // Add to package.json:
 * // "scripts": {
 * //   "clean": "node scripts/clean.cjs",
 * //   "rebuild": "npm run clean && npm run build"
 * // }
 *
 * // Run with default directories:
 * // npm run clean
 *
 * // Run with custom directories:
 * // node scripts/clean.cjs temp coverage
 */

const { promises: fs } = require('fs');
const { join } = require('path');

/**
 * Asynchronously checks if a directory exists at the given path.
 * @param {string} dirPath - The absolute or relative path to the directory.
 * @returns {Promise<boolean>} A promise that resolves to `true` if the directory exists, `false` otherwise.
 */
async function directoryExists(dirPath) {
  try {
    await fs.access(dirPath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Cleans a single directory
 * @param {string} dir - Directory name to clean
 * @returns {Promise<{dir: string, status: string, reason?: string}>} Clean result
 */
async function cleanDirectory(dir) {
  const dirPath = join(process.cwd(), dir);
  
  const exists = await directoryExists(dirPath);
  
  if (!exists) {
    return { dir, status: "skipped", reason: "does not exist" };
  }

  // Use rmdir with recursive option for Node.js 14 compatibility
  // Note: fs.rm was added in Node.js 14.14.0, so we use rmdir for broader compatibility
  try {
    await fs.rmdir(dirPath, { recursive: true });
  } catch (error) {
    // If rmdir fails, try the newer fs.rm if available (Node.js 14.14.0+)
    if (fs.rm) {
      await fs.rm(dirPath, { recursive: true, force: true });
    } else {
      throw error;
    }
  }
  
  return { dir, status: "success" };
}

/**
 * Main function to perform the cleaning operation.
 * It reads command line arguments for target directories or uses defaults ('dist', 'logs').
 * Reports the status of each cleaning attempt.
 */
async function clean() {
  try {
    let dirsToClean = ["dist", "logs"];
    const args = process.argv.slice(2);

    if (args.length > 0) {
      dirsToClean = args;
    }

    console.log(`Attempting to clean directories: ${dirsToClean.join(", ")}`);

    // Use Promise.allSettled for Node.js 14 compatibility
    const results = await Promise.allSettled(
      dirsToClean.map(cleanDirectory)
    );

    results.forEach((result) => {
      if (result.status === "fulfilled") {
        const { dir, status, reason } = result.value;
        if (status === "success") {
          console.log(`Successfully cleaned directory: ${dir}`);
        } else {
          console.log(`Skipped cleaning directory ${dir}: ${reason}.`);
        }
      } else {
        // The error here is the actual error object from the rejected promise
        console.error(
          `Error cleaning a directory (details below):\n`,
          result.reason
        );
      }
    });

    console.log("Clean operation completed.");
  } catch (error) {
    console.error(
      "An unexpected error occurred during the clean script execution:",
      error instanceof Error ? error.message : error
    );
    process.exit(1);
  }
}

// Run the clean function
clean();
